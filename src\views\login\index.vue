<template>
  <div class="bg-gray-100 dark:bg-gray-900 font-sans min-h-screen flex flex-col">
    <!-- 主内容区 -->
    <div class="flex-1 overflow-y-auto p-6 scrollbar-hide">
      <!-- 登录/注册卡片 -->
      <div class="max-w-md mx-auto mt-10">
        <!-- 系统Logo和名称 -->
        <div class="text-center mb-10">
          <div
               class="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <font-awesome-icon icon="graduation-cap" class="text-primary dark:text-primary text-3xl" />
          </div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-1">在线医疗培训系统</h1>
          <p class="text-gray-500 dark:text-gray-400">提升医疗专业技能的学习平台</p>
        </div>

        <!-- 登录表单 -->
        <div v-if="isLogin"
             class="bg-white dark:bg-[#1A1A1A] rounded-xl shadow-lg p-6 transition-all duration-500 hover:shadow-xl">
          <h2 class="text-xl font-bold mb-6 text-center">账号登录</h2>

          <!-- 切换登录方式 -->
          <div class="flex mb-6 border-b border-gray-200 dark:border-gray-700">
            <button @click="toggleLoginType('password')"
                    :class="loginType === 'password' ? 'flex-1 py-2 text-primary border-b-2 border-primary font-medium' : 'flex-1 py-2 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-all duration-300'">
              密码登录
            </button>
            <button @click="toggleLoginType('verification')"
                    :class="loginType === 'verification' ? 'flex-1 py-2 text-primary border-b-2 border-primary font-medium' : 'flex-1 py-2 text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-all duration-300'">
              验证码登录
            </button>
          </div>

          <el-form ref="loginForm" :model="loginForm" :rules="rules" class="space-y-4">
            <!-- 密码登录表单 -->
            <div v-if="loginType === 'password'" class="space-y-4">
              <div class="relative">
                <label for="username"
                       class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">用户名/手机号</label>
                <div class="relative">

                  <el-form-item prop="username" class="m-0">

                    <el-input placeholder="请输入用户名或手机号" v-model="loginForm.username">
                      <i slot="prefix">
                        <font-awesome-icon style="width: 25;" icon="user" />
                      </i>
                    </el-input>

                  </el-form-item>
                </div>
              </div>

              <div class="relative">
                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">密码</label>
                <div class="relative">

                  <el-form-item prop="password" class="m-0">
                    <el-input v-model="loginForm.password" placeholder="请输入密码">
                      <i slot="prefix">
                        <font-awesome-icon style="width: 25;" icon="lock" />
                      </i>
                    </el-input>

                  </el-form-item>

                </div>
              </div>

              <div class="flex items-center justify-between">
                <label class="flex items-center">
                  <input type="checkbox" v-model="loginForm.remember"
                         class="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800">
                  <span class="ml-2 block text-sm text-gray-600 dark:text-gray-400">记住密码</span>
                </label>
                <a href="#"
                   class="text-sm font-medium text-primary hover:text-primary/80 transition-all duration-300">忘记密码?</a>
              </div>

              <el-button type="primary" @click="handleLogin" :loading="loading"
                         class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-md shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                登录
              </el-button>
            </div>

            <!-- 验证码登录表单 -->
            <div v-else class="space-y-4">
              <div class="relative">
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">手机号</label>
                <div class="relative">

                  <el-form-item prop="username" class="m-0">
                    <el-input placeholder="请输入用户名或手机号" v-model="loginForm.username">
                      <i slot="prefix">
                        <font-awesome-icon style="width: 25;" icon="phone" />
                      </i>
                    </el-input>

                  </el-form-item>
                </div>
              </div>

              <div class="relative">
                <label for="verificationCode"
                       class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">验证码</label>
                <div class="flex space-x-2">
                  <div class="relative flex-1">
                    <el-form-item prop="verificationCode" class="m-0">
                      <el-input placeholder="请输入验证码" v-model="loginForm.verificationCode">
                        <i slot="prefix">
                          <font-awesome-icon style="width: 25;" icon="shield-alt" />
                        </i>
                      </el-input>

                    </el-form-item>
                  </div>
                  <button type="button" @click="getVerificationCode" :disabled="countDown > 0"
                          :class="countDown > 0 ? 'opacity-70 cursor-not-allowed' : ''"
                          class="whitespace-nowrap bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-primary font-medium py-2 px-4 rounded-lg transition-all duration-300">
                    {{ countDown > 0 ? `重新获取(${countDown}s)` : '获取验证码' }}
                  </button>
                </div>
              </div>

              <el-button type="primary" @click="handleLogin" :loading="loading"
                         class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-md shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                登录
              </el-button>
            </div>

            <!-- 开发环境模拟登录按钮 -->
            <div v-if="isDev" class="mt-4">
              <el-button type="warning" @click="mockLogin" class="w-full">
                开发环境 - 模拟登录
              </el-button>
            </div>
          </el-form>

          <!-- 注册入口 -->
          <div class="mt-6 text-center">
            <p class="text-gray-600 dark:text-gray-400">
              还没有账号? <span class="text-primary font-medium hover:text-primary/80 transition-all duration-300"
                    @click="toggleLoginForm">立即注册</span>
            </p>
          </div>
        </div>
        <!-- 注册表单 -->
        <div v-else
             class="bg-white dark:bg-[#1A1A1A] rounded-xl shadow-lg p-6 transition-all duration-500 hover:shadow-xl">
          <h2 class="text-xl font-bold mb-6 text-center">账号注册</h2>



          <el-form ref="loginForm" :model="loginForm" :rules="rules" class="space-y-4">


            <!-- 验证码登录表单 -->
            <div class="space-y-4">
              <div class="relative">
                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">手机号</label>
                <div class="relative">

                  <el-form-item prop="username" class="m-0">
                    <el-input placeholder="请输入手机号" v-model="loginForm.username">
                      <i slot="prefix">
                        <font-awesome-icon style="width: 25;" icon="phone" />
                      </i>
                    </el-input>

                  </el-form-item>
                </div>
              </div>

              <div class="relative">
                <label for="verificationCode"
                       class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">验证码</label>
                <div class="flex space-x-2">
                  <div class="relative flex-1">
                    <el-form-item prop="verificationCode" class="m-0">
                      <el-input placeholder="请输入验证码" v-model="loginForm.verificationCode">
                        <i slot="prefix">
                          <font-awesome-icon style="width: 25;" icon="shield-alt" />
                        </i>
                      </el-input>

                    </el-form-item>
                  </div>
                  <button type="button" @click="getVerificationCode" :disabled="countDown > 0"
                          :class="countDown > 0 ? 'opacity-70 cursor-not-allowed' : ''"
                          class="whitespace-nowrap bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-primary font-medium py-2 px-4 rounded-lg transition-all duration-300">
                    {{ countDown > 0 ? `重新获取(${countDown}s)` : '获取验证码' }}
                  </button>
                </div>
              </div>
              <button type="button" @click="handleRegister" :disabled="countDown > 0">
                注册
              </button>
              <el-button type="primary" @click="handleRegister" :loading="loading"
                         class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-md shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                注册
              </el-button>
            </div>
            <div class="mt-4">
              <p class="text-gray-600 dark:text-gray-400">
                已有账号? <span class="text-primary font-medium hover:text-primary/80 transition-all duration-300"
                      @click="backToLogin">登录</span>
              </p>


            </div>

          </el-form>


        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { login, getVerificationCode, register, verifyVerificationCode } from '@/api/user';

// 空行占位符，确保替换正确

export default {
  name: 'Login',
  data() {
    return {
      isDev: process.env.NODE_ENV === 'development',
      isLogin: true,
      loginForm: {
        username: '',
        password: '',
        verificationCode: '',
        remember: false
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名/手机号', trigger: 'blur' },
          // { pattern: /^1[3-9]\d{9}$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/, message: '请输入有效的用户名或手机号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在6-20个字符之间', trigger: 'blur' }
        ],
        verificationCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { len: 4, message: '验证码长度为4个字符', trigger: 'blur' }
        ]
      },
      loginType: 'password',
      showPassword: false,
      loading: false,
      countDown: 0
    }

  },
  methods: {
    // 手动映射toggleDarkMode action
    toggleDarkMode() {
      return this.$store.dispatch('toggleDarkMode');
    },
    // 切换登录注册模式
    toggleLoginForm() {
      this.isLogin = !this.isLogin;
      // 重置表单
      this.$refs.loginForm.resetFields();
    },
    // 返回登录
    backToLogin() {
      this.isLogin = true;
      this.loginType = 'password';

    },
    // 验证验证码
    handleVerifyCode() {
      console.log('验证验证码');
      return verifyVerificationCode({
        PhoneNumber: this.loginForm.username,
        Code: this.loginForm.verificationCode
      }).then((res) => {
        if (res.data.success) {
          this.$message.success('验证码验证成功');
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 注册
    async handleRegister() {
      console.log('注册');
      // await this.handleVerifyCode();

      register({
        Username: "",
        Password: "",
        Phone: this.loginForm.username,
        Usertype: '1',
        RealName: "",
        IsCertified: "",
        RecordState: "",
        IsDel: "",
        IsUpload: "",

      }).then((res) => {
        if (res.data.success) {
          this.$message.success('注册成功');
        } else {
          this.$message.error(res.data.message);
        }
      });

    },
    // 模拟登录
    mockLogin() {
      // 设置模拟token和用户信息
      const mockToken = 'mock_token_' + Date.now();
      const mockUserInfo = {
        id: '1',
        username: 'developer',
        name: '开发人员',
        avatar: '/default-avatar.png',
        roles: ['admin'],
        department: '研发部',
        position: '前端开发'
      };

      // 更新Vuex状态
      this.$store.commit('user/SET_TOKEN', mockToken);
      this.$store.commit('user/SET_USER_INFO', mockUserInfo);
      this.$store.commit('user/SET_ROLES', ['admin']);

      // 设置localStorage以便持久化
      localStorage.setItem('mockToken', mockToken);

      this.$message.success('模拟登录成功，即将跳转到首页');
      this.$router.push('/home');
    },

    // 切换登录类型
    toggleLoginType(type) {
      console.log(process.env);
      this.loginType = type;
      // 重置表单
      this.$refs.loginForm.resetFields();
    },

    // 切换密码可见性
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },

    // 获取验证码
    getVerificationCode() {
      if (!this.loginForm.username) {
        this.$message.error('请输入手机号');
        return;
      }

      // if (!/^1[3-9]\d{9}$/.test(this.loginForm.username)) {
      //   this.$message.error('请输入有效的手机号');
      //   return;
      // }

      // 发送验证码
      getVerificationCode(this.loginForm.username)
        .then(() => {
          this.$message.success('验证码已发送到您的手机，请注意查收');
          // 开始倒计时
          this.countDown = 60;
          const timer = setInterval(() => {
            this.countDown--;
            if (this.countDown <= 0) {
              clearInterval(timer);
            }
          }, 1000);
        })
        .catch((error) => {
          this.$message.error(error.message || '验证码发送失败，请重试');
        });
    },

    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          login({
            username: this.loginForm.username,
            password: this.loginForm.password,
            verificationCode: this.loginForm.verificationCode
          })
            .then((data) => {
              console.log('登录返回数据:', data);

              // 检查返回数据结构
              if (data) {
                // 提取token，可能在不同的字段中
                const token = data.token || data.Token || data.access_token || data.accessToken;

                if (token) {
                  // 存储token到localStorage（路由守卫需要）
                  localStorage.setItem('token', token);

                  // 如果勾选了记住我，也设置cookie
                  if (this.loginForm.remember) {
                    // 设置7天过期
                    const expires = new Date();
                    expires.setDate(expires.getDate() + 7);
                    document.cookie = `token=${token}; expires=${expires.toUTCString()}; path=/`;
                  } else {
                    // 会话级cookie
                    document.cookie = `token=${token}; path=/`;
                  }

                  // 更新Vuex状态
                  this.$store.commit('user/SET_TOKEN', token);
                  if (data.userInfo || data.user) {
                    this.$store.commit('user/SET_USER_INFO', data.userInfo || data.user);
                  }

                  this.$message.success('登录成功，即将跳转到首页');

                  // 使用 $nextTick 确保状态更新完成后再跳转
                  this.$nextTick(() => {
                    this.$router.push('/home');
                  });
                } else {
                  console.error('登录响应中未找到token字段:', data);
                  this.$message.error('登录失败：未获取到有效的登录凭证');
                }
              } else {
                console.error('登录响应数据为空');
                this.$message.error('登录失败：服务器响应异常');
              }
            })
            .catch((error) => {
              this.$message.error(error.message || '登录失败，请重试');
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    }
  },
  computed: {
    ...mapGetters(['isDarkMode'])
  },
  mounted() {
    // 初始化深色模式
    this.$store.dispatch('initDarkMode');

    // 如果是开发环境且已经有模拟登录的token，则直接跳转
    if (this.isDev && localStorage.getItem('mockToken')) {
      this.$router.push('/home');
    }
  },

};
</script>

<style scoped>
/* 自定义工具类 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 深色模式支持 */
.dark .bg-dark-secondary {
  background-color: #1A1A1A;
}

/* 主色调定义 */
:root {
  --primary-color: #3b82f6;
}

/* 自定义输入框样式 */
.el-input__inner {
  border-radius: 0.5rem !important;
  padding: 0.5rem 0.75rem !important;
  transition: all 0.3s ease !important;
}

/* 自定义按钮样式 */
.el-button {
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease !important;
}

/* 按钮悬停效果 */
.el-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
</style>